# Platform Inconsistencies - File Locations & Line Numbers

## 🔴 CRITICAL PRIORITY ISSUES

### 1. Profile Loading Duplication

#### Files with Duplicate Profile Loading Logic:
- **`src/services/profileService.ts`**
  - Lines 161-186: `loadBaseProfile()` function
  - Lines 199-219: `loadUserProfiles()` function  
  - Lines 589-604: `updateSpecializedProfile()` function
  - Lines 1080-1102: `loadPublicProfiles()` function

- **`src/stores/profileStore.ts`**
  - Lines 108-156: `loadUserProfiles()` function (duplicates service logic)
  - Lines 1061-1107: `loadProfileById()` function (another duplicate)
  - Lines 1139-1299: `fetchProfile()` function (third duplicate)

- **`src/services/api/publicProfiles.ts`**
  - Lines 33-77: `fetchPublicProfile()` function (separate implementation)
  - Lines 134-154: Profile conversion logic (duplicates profileService)

**Impact**: 3-5x redundant API calls for same profile data

### 2. Route Guard Conflicts

#### Duplicate Route Guard Files:
- **`src/router/guards.ts`** (Lines 1-49) - **SHOULD BE REMOVED**
  - Lines 28-36: Profile loading logic that conflicts with enhanced guards
  
- **`src/router/enhancedGuards.ts`** (Lines 1-89) - **KEEP THIS ONE**
  - Lines 38-46: User state checking with caching (better implementation)

**Impact**: Inconsistent authentication behavior, potential security gaps

### 3. Service Initialization Conflicts

#### Files with Duplicate Service Initialization:
- **`src/layouts/DashboardLayout.vue`**
  - Lines 435-445: Service initialization in onMounted
  
- **`src/views/dashboard/Dashboard.vue`**
  - Lines 717-730: **DUPLICATE** service initialization (partially fixed)

**Impact**: 2x slower dashboard loading, duplicate subscriptions

### 4. Component Duplication

#### Identical Components:
- **`src/views/SignIn.vue`** (Lines 1-150) - **KEEP THIS ONE**
- **`src/views/public/auth/SignIn.vue`** (Lines 1-150) - **REMOVE THIS**

**Impact**: Routing confusion, maintenance burden

## 🟡 HIGH PRIORITY ISSUES

### 5. User State Management Overlap

#### Files with Overlapping User State Logic:
- **`src/services/userStateService.ts`**
  - Lines 27-66: `checkUserState()` function with caching
  
- **`src/stores/profileStore.ts`**
  - Lines 425-451: `setCurrentProfile()` function (overlaps with user state)
  
- **`src/stores/auth.ts`**
  - Lines 468-494: `checkSession()` function (another state check)

**Impact**: Race conditions, inconsistent state updates

### 6. Messaging System Overlap

#### Files with Overlapping Messaging Logic:
- **`src/stores/messaging.ts`** (Lines 1-1193)
  - Direct message handling
  
- **`src/stores/notifications.ts`** (Lines 1-114)
  - UI notification system
  
- **`src/stores/userNotifications.ts`** (Lines 1-195)
  - Database notification system
  
- **`src/services/notificationService.ts`** (Lines 1-120)
  - Another notification layer

**Impact**: Confusing notification system, potential message loss

### 7. Caching Strategy Conflicts

#### Files with Different Caching Approaches:
- **`src/services/userStateService.ts`**
  - Lines 33-48: sessionStorage caching (5 min TTL)
  
- **`src/stores/messaging.ts`**
  - Lines 10-18: Map-based table existence caching
  
- **`src/components/feed/FeedContainer.vue`**
  - Lines 499-500: Component-level caching (30 sec TTL)
  
- **`src/stores/profileStore.ts`**
  - No consistent caching strategy

**Impact**: Stale data, memory leaks, inconsistent UX

## ⚡ PERFORMANCE ANTI-PATTERNS

### 8. Redundant API Calls

#### Files with Redundant Database Calls:
- **`src/stores/activityNotifications.ts`**
  - Lines 99-108: Table existence check without caching
  
- **`src/stores/messaging.ts`**
  - Lines 438-443: Repeated table existence checks
  
- **`src/components/feed/FeedContainer.vue`**
  - Lines 697-921: Multiple simultaneous data fetching

**Impact**: Poor performance, increased server load

### 9. Inefficient Data Loading

#### Files with Loading Inefficiencies:
- **`src/stores/profileStore.ts`**
  - Lines 1075-1076: Loading all profiles when only one needed
  
- **`src/services/profileService.ts`**
  - Lines 199-219: No pagination in profile loading
  
- **`src/router/enhancedGuards.ts`**
  - Lines 38-46: Blocking operations in route guards

**Impact**: Slow page loads, poor mobile performance

## 📊 DATA CONSISTENCY ISSUES

### 10. Profile Data Type Inconsistencies

#### Files with Conflicting Profile Types:
- **`src/services/profileService.ts`**
  - Lines 14-35: `BaseProfile` interface definition
  
- **`src/services/api/publicProfiles.ts`**
  - Lines 7-25: `PublicProfile` interface (overlapping fields)
  
- **`src/stores/profileStore.ts`**
  - Mixed usage of both types without clear conversion

**Impact**: Type safety issues, data integrity problems

### 11. Error Handling Inconsistencies

#### Files with Different Error Patterns:
- **`src/services/profileService.ts`**
  - Lines 170-185: Throws errors
  
- **`src/stores/profileStore.ts`**
  - Lines 1292-1294: Returns null on error
  
- **`src/services/api/publicProfiles.ts`**
  - Lines 44-50: Silent error handling

**Impact**: Inconsistent user experience, difficult debugging

## QUICK REFERENCE: FILES TO MODIFY/REMOVE

### Files to Remove:
- `src/router/guards.ts` (entire file)
- `src/views/public/auth/SignIn.vue` (entire file)

### Files to Consolidate:
- `src/services/profileService.ts` → Merge into ProfileManager
- `src/stores/profileStore.ts` → Update to use ProfileManager
- `src/services/api/publicProfiles.ts` → Merge into ProfileManager

### Files to Update:
- `src/layouts/DashboardLayout.vue` → Use ServiceCoordinator
- `src/views/dashboard/Dashboard.vue` → Remove duplicate initialization
- `src/router/enhancedGuards.ts` → Add caching improvements
- `src/router/index.ts` → Update route references

### New Files to Create:
- `src/services/ProfileManager.ts` → Unified profile management
- `src/services/ServiceCoordinator.ts` → Service initialization coordination
- `src/services/CacheManager.ts` → Unified caching strategy

## ESTIMATED EFFORT

### Critical Priority (Week 1-2): 
- **40 hours** - Profile consolidation, route guard fixes, service coordination

### High Priority (Week 3-4):
- **30 hours** - Messaging consolidation, caching unification

### Medium Priority (Week 5-6):
- **20 hours** - Error handling standardization, performance optimization

**Total Estimated Effort**: 90 hours (approximately 6 weeks for one developer)

## RISK ASSESSMENT

### High Risk Changes:
- Profile loading consolidation (affects core functionality)
- Route guard modifications (affects authentication)

### Medium Risk Changes:
- Service initialization coordination (affects dashboard loading)
- Messaging system consolidation (affects user communication)

### Low Risk Changes:
- Component duplication removal (isolated changes)
- Error handling standardization (incremental improvements)

## TESTING STRATEGY

### Critical Path Testing:
1. Authentication flow (sign-in, sign-out, route protection)
2. Profile loading and display (dashboard, public profiles)
3. Service initialization (dashboard loading, real-time features)

### Performance Testing:
1. Dashboard load time measurement
2. API call counting and deduplication verification
3. Memory usage monitoring for cache efficiency

### Regression Testing:
1. All existing functionality must continue to work
2. No new console errors or warnings
3. Consistent user experience across all contexts
