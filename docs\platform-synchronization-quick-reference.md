# Platform Synchronization Quick Reference

## Overview

This document provides quick reference information for developers working on platform synchronization fixes. Use this for common patterns, code snippets, and troubleshooting.

## Common Code Patterns

### Service Creation Pattern

```typescript
// Standard service pattern with error handling and caching
export function useMyService() {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const cache = ref<Map<string, any>>(new Map())

  async function myMethod(param: string): Promise<any> {
    try {
      loading.value = true
      error.value = null
      
      // Check cache first
      if (cache.value.has(param)) {
        return cache.value.get(param)
      }
      
      // Perform operation
      const result = await performOperation(param)
      
      // Cache result
      cache.value.set(param, result)
      
      return result
    } catch (err: any) {
      error.value = err.message || 'Operation failed'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    myMethod,
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    clearCache: () => cache.value.clear()
  }
}
```

### Real-time Subscription Pattern

```typescript
// Standard real-time subscription pattern
export function useRealtimeSubscription() {
  const subscriptions = ref<any[]>([])

  function subscribe(table: string, filter: string, callback: Function) {
    const subscription = supabase
      .channel(`${table}-${Date.now()}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table,
          filter
        },
        callback
      )
      .subscribe()

    subscriptions.value.push(subscription)
    return subscription
  }

  function cleanup() {
    subscriptions.value.forEach(sub => {
      supabase.removeChannel(sub)
    })
    subscriptions.value = []
  }

  onUnmounted(() => {
    cleanup()
  })

  return { subscribe, cleanup }
}
```

### Component Update Pattern

```vue
<template>
  <div>
    <div v-if="loading" class="loading-state">
      <q-spinner color="primary" size="2em" />
      <p>Loading...</p>
    </div>
    
    <div v-else-if="error" class="error-state">
      <q-icon name="error" color="negative" size="2em" />
      <p>{{ error }}</p>
      <q-btn @click="retry" label="Retry" />
    </div>
    
    <div v-else class="content">
      <!-- Real content here -->
    </div>
  </div>
</template>

<script setup lang="ts">
// Standard component pattern
const props = defineProps<{
  userId: string
}>()

const service = useMyService()
const data = ref(null)
const loading = computed(() => service.loading.value)
const error = computed(() => service.error.value)

async function loadData() {
  try {
    data.value = await service.getData(props.userId)
  } catch (err) {
    // Error handled by service
  }
}

async function retry() {
  await loadData()
}

// Load data on mount and when userId changes
watch(() => props.userId, loadData, { immediate: true })
</script>
```

## Database Query Patterns

### Count Queries with Error Handling

```typescript
async function getCount(table: string, filter: any): Promise<number> {
  try {
    const { count, error } = await supabase
      .from(table)
      .select('*', { count: 'exact', head: true })
      .match(filter)

    if (error) {
      // Handle specific error cases
      if (error.code === 'PGRST200' || error.message?.includes('does not exist')) {
        console.warn(`Table ${table} does not exist, returning 0`)
        return 0
      }
      throw error
    }

    return count || 0
  } catch (err) {
    console.error(`Error counting ${table}:`, err)
    return 0 // Graceful fallback
  }
}
```

### Batch Data Fetching

```typescript
async function getBatchData(userId: string) {
  const [posts, connections, events, groups] = await Promise.allSettled([
    getPostCount(userId),
    getConnectionCount(userId),
    getEventCount(userId),
    getGroupCount(userId)
  ])

  return {
    posts: posts.status === 'fulfilled' ? posts.value : 0,
    connections: connections.status === 'fulfilled' ? connections.value : 0,
    events: events.status === 'fulfilled' ? events.value : 0,
    groups: groups.status === 'fulfilled' ? groups.value : 0
  }
}
```

## Testing Patterns

### Service Testing

```typescript
// tests/services/myService.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useMyService } from '@/services/myService'

// Mock Supabase
vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({ data: mockData, error: null }))
        }))
      }))
    }))
  }
}))

describe('MyService', () => {
  let service: ReturnType<typeof useMyService>

  beforeEach(() => {
    service = useMyService()
  })

  it('should fetch data successfully', async () => {
    const result = await service.getData('test-id')
    expect(result).toBeDefined()
    expect(service.loading.value).toBe(false)
    expect(service.error.value).toBeNull()
  })

  it('should handle errors gracefully', async () => {
    // Mock error response
    vi.mocked(supabase.from).mockReturnValueOnce({
      select: () => ({
        eq: () => ({
          single: () => Promise.resolve({ data: null, error: { message: 'Test error' } })
        })
      })
    } as any)

    await expect(service.getData('test-id')).rejects.toThrow('Test error')
    expect(service.error.value).toBe('Test error')
  })
})
```

### Component Testing

```typescript
// tests/components/MyComponent.test.ts
import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import MyComponent from '@/components/MyComponent.vue'

// Mock the service
vi.mock('@/services/myService', () => ({
  useMyService: () => ({
    getData: vi.fn(() => Promise.resolve(mockData)),
    loading: ref(false),
    error: ref(null)
  })
}))

describe('MyComponent', () => {
  it('should render data correctly', async () => {
    const wrapper = mount(MyComponent, {
      props: { userId: 'test-id' }
    })

    await wrapper.vm.$nextTick()
    
    expect(wrapper.text()).toContain('Expected content')
    expect(wrapper.find('.loading-state').exists()).toBe(false)
  })
})
```

## Troubleshooting Guide

### Common Issues and Solutions

#### Issue: Hardcoded Values Still Showing
```typescript
// ❌ Wrong - hardcoded value
<div class="text-h5">{{ profile.posts || 0 }}</div>

// ✅ Correct - real data
<div class="text-h5">{{ userStats?.posts || 0 }}</div>
```

#### Issue: Cache Not Invalidating
```typescript
// ❌ Wrong - no cache invalidation
async function updateUserData(userId: string) {
  await updateDatabase(userId)
  // Cache still has old data
}

// ✅ Correct - invalidate cache
async function updateUserData(userId: string) {
  await updateDatabase(userId)
  profileStatsService.clearCache()
  // Or invalidate specific cache entry
  cache.value.delete(userId)
}
```

#### Issue: Real-time Subscriptions Not Working
```typescript
// ❌ Wrong - subscription not properly set up
const subscription = supabase.channel('my-channel')
// Missing .on() and .subscribe()

// ✅ Correct - complete subscription setup
const subscription = supabase
  .channel('my-channel')
  .on('postgres_changes', { /* config */ }, callback)
  .subscribe()

// Don't forget cleanup
onUnmounted(() => {
  supabase.removeChannel(subscription)
})
```

#### Issue: Performance Problems with Large Datasets
```typescript
// ❌ Wrong - fetching all data
const { data } = await supabase.from('posts').select('*')

// ✅ Correct - pagination and limits
const { data } = await supabase
  .from('posts')
  .select('*')
  .range(start, end)
  .limit(20)
```

### Debugging Commands

```bash
# Check for mock data in codebase
grep -r "mock" src/ --include="*.vue" --include="*.ts"

# Find hardcoded numbers that might be counters
grep -r "\|\| 0" src/ --include="*.vue"

# Check for console.log statements that should be replaced
grep -r "console\.log" src/ --include="*.ts" --include="*.vue"

# Find TODO comments related to synchronization
grep -r "TODO.*sync\|TODO.*mock\|TODO.*real" src/
```

### Performance Monitoring

```typescript
// Add to components for performance monitoring
const startTime = performance.now()

// Your operation here

const endTime = performance.now()
if (endTime - startTime > 1000) {
  console.warn(`Slow operation detected: ${endTime - startTime}ms`)
}
```

## File Structure Reference

```
src/
├── services/
│   ├── contentMatchingService.ts      # Real content matching
│   ├── profileStatsService.ts         # Real user statistics
│   ├── realtimeCounterService.ts      # Real-time updates
│   └── feedPersonalizationService.ts  # Feed personalization
├── components/
│   ├── messaging/
│   │   └── UnifiedMessageDialog.vue   # Cross-context messaging
│   └── profile/
│       └── UnifiedProfileView.vue     # Updated with real stats
├── utils/
│   ├── logger.ts                      # Centralized logging
│   └── performanceMonitor.ts          # Performance tracking
└── tests/
    ├── services/                      # Service tests
    ├── components/                    # Component tests
    └── integration/                   # Integration tests
```

## Quick Commands

```bash
# Run specific test suite
npm run test -- --grep "ProfileStats"

# Run tests with coverage
npm run test:coverage

# Check TypeScript errors
npm run type-check

# Run linting
npm run lint

# Build for production
npm run build

# Start development server
npm run dev
```

This quick reference provides the essential patterns and troubleshooting information needed for implementing platform synchronization fixes efficiently.
