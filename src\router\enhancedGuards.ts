import { Router, RouteLocationNormalized } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useUserState } from '../services/userStateService'

// Cache for authentication checks to prevent redundant operations
const authCache = new Map<string, { result: boolean; timestamp: number }>()
const AUTH_CACHE_TTL = 300000 // 5 minutes

/**
 * Unified Route Guards
 *
 * Consolidates all authentication and user state checking logic with intelligent caching
 * to prevent redundant operations and improve performance.
 */
export function setupEnhancedRouteGuards(router: Router) {
  router.beforeEach(async (to: RouteLocationNormalized, from: RouteLocationNormalized) => {
    const authStore = useAuthStore()
    const { userState, isNewUser, hasIncompleteProfile, checkUserState } = useUserState()

    // Skip auth check for public routes and auth callbacks
    if (to.path === '/auth/callback' || to.path === '/auth/verify' || to.path === '/') {
      return true
    }

    // Ensure auth is initialized before proceeding
    if (!authStore.isInitialized) {
      console.log('RouteGuards: Initializing authentication...')
      await authStore.checkSession()
    }

    // Handle routes that require authentication
    if (to.matched.some(record => record.meta.requiresAuth)) {
      if (!authStore.isAuthenticated) {
        console.log('RouteGuards: Authentication required, redirecting to sign-in')
        return '/sign-in'
      }

      // Cached user state check to prevent redundant database calls
      const userId = authStore.currentUser?.id
      if (userId) {
        const cacheKey = `userState_${userId}`
        const cached = authCache.get(cacheKey)
        const now = Date.now()

        // Only check user state if not cached or cache expired
        if (!cached || (now - cached.timestamp) > AUTH_CACHE_TTL) {
          try {
            console.log('RouteGuards: Checking user state...')
            await checkUserState()
            authCache.set(cacheKey, { result: true, timestamp: now })
            console.log('RouteGuards: User state cached')
          } catch (error) {
            console.error('RouteGuards: Error checking user state:', error)
            // Don't block navigation on user state errors
          }
        } else {
          console.log('RouteGuards: Using cached user state')
        }
      }

      // Redirect based on user state if needed
      if (to.matched.some(record => record.meta.requiresCompleteProfile) &&
          hasIncompleteProfile.value) {
        console.log('Route requires complete profile but user has incomplete profile, redirecting to profile dashboard')
        return '/dashboard/profile'
      }

      // Redirect new users to profile creation
      if (to.matched.some(record => record.meta.requiresProfile) &&
          isNewUser.value) {
        console.log('Route requires profile but user is new, redirecting to profile creation')
        return '/dashboard/profile/create'
      }


    }

    // Check if route is guest-only (like sign-in)
    if (to.matched.some(record => record.meta.guestOnly)) {
      if (authStore.isAuthenticated) {
        console.log('Route is guest-only but user is authenticated, redirecting to dashboard')
        return '/dashboard'
      }
    }

    // Check if route requires no profile
    if (to.matched.some(record => record.meta.requiresNoProfile)) {
      if (!isNewUser.value) {
        console.log('Route requires no profile but user has a profile, redirecting to dashboard')
        return '/dashboard'
      }
    }

    return true
  })

  // After each navigation, scroll to top
  router.afterEach((to, from) => {
    // Scroll to top only if the route path changed
    if (to.path !== from.path) {
      window.scrollTo(0, 0)
    }
  })
}

/**
 * Clear authentication cache (useful for testing or when user logs out)
 */
export function clearAuthCache(): void {
  authCache.clear()
  console.log('RouteGuards: Authentication cache cleared')
}

/**
 * Get cache statistics for monitoring
 */
export function getAuthCacheStats() {
  const now = Date.now()
  let validEntries = 0
  let expiredEntries = 0

  for (const entry of authCache.values()) {
    if (now - entry.timestamp < AUTH_CACHE_TTL) {
      validEntries++
    } else {
      expiredEntries++
    }
  }

  return {
    totalEntries: authCache.size,
    validEntries,
    expiredEntries,
    cacheHitRate: validEntries / authCache.size || 0
  }
}